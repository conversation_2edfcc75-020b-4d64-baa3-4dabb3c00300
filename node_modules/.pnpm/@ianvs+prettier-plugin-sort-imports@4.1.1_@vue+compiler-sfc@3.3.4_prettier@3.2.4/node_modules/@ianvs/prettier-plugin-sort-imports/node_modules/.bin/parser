#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/parser/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/parser/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/parser/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/parser/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules/@babel/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/@babel+parser@7.28.0/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@babel+parser@7.28.0/node_modules/@babel/parser/bin/babel-parser.js" "$@"
else
  exec node  "$basedir/../../../../../../@babel+parser@7.28.0/node_modules/@babel/parser/bin/babel-parser.js" "$@"
fi
