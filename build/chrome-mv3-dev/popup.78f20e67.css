@import "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap";
@tailwind base;

@tailwind components;

@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;

    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-9 px-4 py-2;
  }

  .btn-lg {
    @apply h-10 px-8;
  }

  .input {
    @apply flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }

  .status-badge {
    @apply inline-flex items-center rounded-full px-2 py-1 text-xs font-medium;
  }

  .status-success {
    @apply status-badge bg-success-100 text-success-800;
  }

  .status-error {
    @apply status-badge bg-error-100 text-error-800;
  }

  .status-warning {
    @apply status-badge bg-warning-100 text-warning-800;
  }

  .status-info {
    @apply status-badge bg-primary-100 text-primary-800;
  }

  .method-get {
    @apply bg-success-100 text-success-800;
  }

  .method-post {
    @apply bg-primary-100 text-primary-800;
  }

  .method-put {
    @apply bg-warning-100 text-warning-800;
  }

  .method-delete {
    @apply bg-error-100 text-error-800;
  }

  .method-patch {
    @apply bg-purple-100 text-purple-800;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: none;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */
