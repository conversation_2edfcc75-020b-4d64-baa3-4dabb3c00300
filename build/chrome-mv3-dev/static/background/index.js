(function(define){var __define; typeof define === "function" && (__define=define,define=null);
// modules are defined as an array
// [ module function, map of requires ]
//
// map of requires is short require name -> numeric require
//
// anything defined in a previous bundle is accessed via the
// orig method which is the require for previous bundles

(function (modules, entry, mainEntry, parcelRequireName, globalName) {
  /* eslint-disable no-undef */
  var globalObject =
    typeof globalThis !== 'undefined'
      ? globalThis
      : typeof self !== 'undefined'
      ? self
      : typeof window !== 'undefined'
      ? window
      : typeof global !== 'undefined'
      ? global
      : {};
  /* eslint-enable no-undef */

  // Save the require from previous bundle to this closure if any
  var previousRequire =
    typeof globalObject[parcelRequireName] === 'function' &&
    globalObject[parcelRequireName];

  var cache = previousRequire.cache || {};
  // Do not use `require` to prevent Webpack from trying to bundle this call
  var nodeRequire =
    typeof module !== 'undefined' &&
    typeof module.require === 'function' &&
    module.require.bind(module);

  function newRequire(name, jumped) {
    if (!cache[name]) {
      if (!modules[name]) {
        // if we cannot find the module within our internal map or
        // cache jump to the current global require ie. the last bundle
        // that was added to the page.
        var currentRequire =
          typeof globalObject[parcelRequireName] === 'function' &&
          globalObject[parcelRequireName];
        if (!jumped && currentRequire) {
          return currentRequire(name, true);
        }

        // If there are other bundles on this page the require from the
        // previous one is saved to 'previousRequire'. Repeat this as
        // many times as there are bundles until the module is found or
        // we exhaust the require chain.
        if (previousRequire) {
          return previousRequire(name, true);
        }

        // Try the node require function if it exists.
        if (nodeRequire && typeof name === 'string') {
          return nodeRequire(name);
        }

        var err = new Error("Cannot find module '" + name + "'");
        err.code = 'MODULE_NOT_FOUND';
        throw err;
      }

      localRequire.resolve = resolve;
      localRequire.cache = {};

      var module = (cache[name] = new newRequire.Module(name));

      modules[name][0].call(
        module.exports,
        localRequire,
        module,
        module.exports,
        this
      );
    }

    return cache[name].exports;

    function localRequire(x) {
      var res = localRequire.resolve(x);
      return res === false ? {} : newRequire(res);
    }

    function resolve(x) {
      var id = modules[name][1][x];
      return id != null ? id : x;
    }
  }

  function Module(moduleName) {
    this.id = moduleName;
    this.bundle = newRequire;
    this.exports = {};
  }

  newRequire.isParcelRequire = true;
  newRequire.Module = Module;
  newRequire.modules = modules;
  newRequire.cache = cache;
  newRequire.parent = previousRequire;
  newRequire.register = function (id, exports) {
    modules[id] = [
      function (require, module) {
        module.exports = exports;
      },
      {},
    ];
  };

  Object.defineProperty(newRequire, 'root', {
    get: function () {
      return globalObject[parcelRequireName];
    },
  });

  globalObject[parcelRequireName] = newRequire;

  for (var i = 0; i < entry.length; i++) {
    newRequire(entry[i]);
  }

  if (mainEntry) {
    // Expose entry point to Node, AMD or browser globals
    // Based on https://github.com/ForbesLindesay/umd/blob/master/template.js
    var mainExports = newRequire(mainEntry);

    // CommonJS
    if (typeof exports === 'object' && typeof module !== 'undefined') {
      module.exports = mainExports;

      // RequireJS
    } else if (typeof define === 'function' && define.amd) {
      define(function () {
        return mainExports;
      });

      // <script>
    } else if (globalName) {
      this[globalName] = mainExports;
    }
  }
})({"l2tdl":[function(require,module,exports) {
var u = globalThis.process?.argv || [];
var h = ()=>globalThis.process?.env || {};
var B = new Set(u), _ = (e)=>B.has(e), G = u.filter((e)=>e.startsWith("--") && e.includes("=")).map((e)=>e.split("=")).reduce((e, [t, o])=>(e[t] = o, e), {});
var U = _("--dry-run"), g = ()=>_("--verbose") || h().VERBOSE === "true", N = g();
var m = (e = "", ...t)=>console.log(e.padEnd(9), "|", ...t);
var y = (...e)=>console.error("\uD83D\uDD34 ERROR".padEnd(9), "|", ...e), v = (...e)=>m("\uD83D\uDD35 INFO", ...e), f = (...e)=>m("\uD83D\uDFE0 WARN", ...e), M = 0, i = (...e)=>g() && m(`\u{1F7E1} ${M++}`, ...e);
var b = ()=>{
    let e = globalThis.browser?.runtime || globalThis.chrome?.runtime, t = ()=>setInterval(e.getPlatformInfo, 24e3);
    e.onStartup.addListener(t), t();
};
var n = {
    "isContentScript": false,
    "isBackground": true,
    "isReact": false,
    "runtimes": [
        "background-service-runtime"
    ],
    "host": "localhost",
    "port": 1815,
    "entryFilePath": "/Users/<USER>/Projects/api-interceptor-extension/.plasmo/static/background/index.ts",
    "bundleId": "c338908e704c91f1",
    "envHash": "d99a5ffa57acd638",
    "verbose": "false",
    "secure": false,
    "serverPort": 65011
};
module.bundle.HMR_BUNDLE_ID = n.bundleId;
globalThis.process = {
    argv: [],
    env: {
        VERBOSE: n.verbose
    }
};
var D = module.bundle.Module;
function H(e) {
    D.call(this, e), this.hot = {
        data: module.bundle.hotData[e],
        _acceptCallbacks: [],
        _disposeCallbacks: [],
        accept: function(t) {
            this._acceptCallbacks.push(t || function() {});
        },
        dispose: function(t) {
            this._disposeCallbacks.push(t);
        }
    }, module.bundle.hotData[e] = void 0;
}
module.bundle.Module = H;
module.bundle.hotData = {};
var c = globalThis.browser || globalThis.chrome || null;
function R() {
    return !n.host || n.host === "0.0.0.0" ? location.protocol.indexOf("http") === 0 ? location.hostname : "localhost" : n.host;
}
function x() {
    return !n.host || n.host === "0.0.0.0" ? "localhost" : n.host;
}
function d() {
    return n.port || location.port;
}
var P = "__plasmo_runtime_page_", S = "__plasmo_runtime_script_";
var O = `${n.secure ? "https" : "http"}://${R()}:${d()}/`;
async function k(e = 1470) {
    for(;;)try {
        await fetch(O);
        break;
    } catch  {
        await new Promise((o)=>setTimeout(o, e));
    }
}
if (c.runtime.getManifest().manifest_version === 3) {
    let e = c.runtime.getURL("/__plasmo_hmr_proxy__?url=");
    globalThis.addEventListener("fetch", function(t) {
        let o = t.request.url;
        if (o.startsWith(e)) {
            let s = new URL(decodeURIComponent(o.slice(e.length)));
            s.hostname === n.host && s.port === `${n.port}` ? (s.searchParams.set("t", Date.now().toString()), t.respondWith(fetch(s).then((r)=>new Response(r.body, {
                    headers: {
                        "Content-Type": r.headers.get("Content-Type") ?? "text/javascript"
                    }
                })))) : t.respondWith(new Response("Plasmo HMR", {
                status: 200,
                statusText: "Testing"
            }));
        }
    });
}
function E(e, t) {
    let { modules: o } = e;
    return o ? !!o[t] : !1;
}
function C(e = d()) {
    let t = x();
    return `${n.secure || location.protocol === "https:" && !/localhost|127.0.0.1|0.0.0.0/.test(t) ? "wss" : "ws"}://${t}:${e}/`;
}
function L(e) {
    typeof e.message == "string" && y("[plasmo/parcel-runtime]: " + e.message);
}
function T(e) {
    if (typeof globalThis.WebSocket > "u") return;
    let t = new WebSocket(C(Number(d()) + 1));
    return t.addEventListener("message", async function(o) {
        let s = JSON.parse(o.data);
        await e(s);
    }), t.addEventListener("error", L), t;
}
function A(e) {
    if (typeof globalThis.WebSocket > "u") return;
    let t = new WebSocket(C());
    return t.addEventListener("message", async function(o) {
        let s = JSON.parse(o.data);
        if (s.type === "update" && await e(s.assets), s.type === "error") for (let r of s.diagnostics.ansi){
            let l = r.codeframe || r.stack;
            f("[plasmo/parcel-runtime]: " + r.message + `
` + l + `

` + r.hints.join(`
`));
        }
    }), t.addEventListener("error", L), t.addEventListener("open", ()=>{
        v(`[plasmo/parcel-runtime]: Connected to HMR server for ${n.entryFilePath}`);
    }), t.addEventListener("close", ()=>{
        f(`[plasmo/parcel-runtime]: Connection to the HMR server is closed for ${n.entryFilePath}`);
    }), t;
}
var w = module.bundle.parent, a = {
    buildReady: !1,
    bgChanged: !1,
    csChanged: !1,
    pageChanged: !1,
    scriptPorts: new Set,
    pagePorts: new Set
};
async function p(e = !1) {
    if (e || a.buildReady && a.pageChanged) {
        i("BGSW Runtime - reloading Page");
        for (let t of a.pagePorts)t.postMessage(null);
    }
    if (e || a.buildReady && (a.bgChanged || a.csChanged)) {
        i("BGSW Runtime - reloading CS");
        let t = await c?.tabs.query({
            active: !0
        });
        for (let o of a.scriptPorts){
            let s = t.some((r)=>r.id === o.sender.tab?.id);
            o.postMessage({
                __plasmo_cs_active_tab__: s
            });
        }
        c.runtime.reload();
    }
}
if (!w || !w.isParcelRequire) {
    b();
    let e = A(async (t)=>{
        i("BGSW Runtime - On HMR Update"), a.bgChanged ||= t.filter((s)=>s.envHash === n.envHash).some((s)=>E(module.bundle, s.id));
        let o = t.find((s)=>s.type === "json");
        if (o) {
            let s = new Set(t.map((l)=>l.id)), r = Object.values(o.depsByBundle).map((l)=>Object.values(l)).flat();
            a.bgChanged ||= r.every((l)=>s.has(l));
        }
        p();
    });
    e.addEventListener("open", ()=>{
        let t = setInterval(()=>e.send("ping"), 24e3);
        e.addEventListener("close", ()=>clearInterval(t));
    }), e.addEventListener("close", async ()=>{
        await k(), p(!0);
    });
}
T(async (e)=>{
    switch(i("BGSW Runtime - On Build Repackaged"), e.type){
        case "build_ready":
            a.buildReady ||= !0, p();
            break;
        case "cs_changed":
            a.csChanged ||= !0, p();
            break;
    }
});
c.runtime.onConnect.addListener(function(e) {
    let t = e.name.startsWith(P), o = e.name.startsWith(S);
    if (t || o) {
        let s = t ? a.pagePorts : a.scriptPorts;
        s.add(e), e.onDisconnect.addListener(()=>{
            s.delete(e);
        }), e.onMessage.addListener(function(r) {
            i("BGSW Runtime - On source changed", r), r.__plasmo_cs_changed__ && (a.csChanged ||= !0), r.__plasmo_page_changed__ && (a.pageChanged ||= !0), p();
        });
    }
});
c.runtime.onMessage.addListener(function(t) {
    return t.__plasmo_full_reload__ && (i("BGSW Runtime - On top-level code changed"), p()), !0;
});

},{}],"8oeFb":[function(require,module,exports) {
var _background = require("../../../background");

},{"../../../background":"14rpM"}],"14rpM":[function(require,module,exports) {
// API Interceptor Background Script
// This script intercepts all network requests and stores them for display
var _storage = require("./utils/storage");
// Store for ongoing requests
const pendingRequests = new Map();
// Capture status
let isCapturing = true;
// Generate unique request ID
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
// Get request body from details
function getRequestBody(details) {
    if (!details.requestBody) return undefined;
    if (details.requestBody.formData) return JSON.stringify(details.requestBody.formData);
    if (details.requestBody.raw) {
        const decoder = new TextDecoder();
        return details.requestBody.raw.map((data)=>decoder.decode(data.bytes)).join("");
    }
    return undefined;
}
// Store request data
async function storeRequestData(requestData) {
    // Only store if capturing is enabled
    if (!isCapturing) return;
    try {
        await (0, _storage.StorageManager).storeRequest(requestData);
        // Notify popup if it's open
        chrome.runtime.sendMessage({
            type: "NEW_REQUEST",
            data: requestData
        }).catch(()=>{
        // Ignore errors if popup is not open
        });
    } catch (error) {
        console.error("Error storing request data:", error);
    }
}
// Listen for request start
chrome.webRequest.onBeforeRequest.addListener((details)=>{
    const requestId = generateRequestId();
    const requestData = {
        id: requestId,
        url: details.url,
        method: details.method,
        timestamp: Date.now(),
        requestHeaders: [],
        requestBody: getRequestBody(details),
        type: details.type,
        tabId: details.tabId
    };
    pendingRequests.set(details.requestId, requestData);
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "requestBody"
]);
// Listen for request headers
chrome.webRequest.onBeforeSendHeaders.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) requestData.requestHeaders = details.requestHeaders || [];
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "requestHeaders"
]);
// Listen for response headers
chrome.webRequest.onHeadersReceived.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.responseHeaders = details.responseHeaders || [];
        requestData.status = details.statusCode;
        requestData.statusText = details.statusLine;
    }
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "responseHeaders"
]);
// Listen for request completion
chrome.webRequest.onCompleted.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.duration = Date.now() - requestData.timestamp;
        requestData.status = details.statusCode;
        requestData.statusText = details.statusLine;
        // Store the completed request
        storeRequestData(requestData);
        // Clean up
        pendingRequests.delete(details.requestId);
    }
}, {
    urls: [
        "<all_urls>"
    ]
});
// Listen for request errors
chrome.webRequest.onErrorOccurred.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.duration = Date.now() - requestData.timestamp;
        requestData.status = 0;
        requestData.statusText = details.error;
        // Store the failed request
        storeRequestData(requestData);
        // Clean up
        pendingRequests.delete(details.requestId);
    }
}, {
    urls: [
        "<all_urls>"
    ]
});
// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse)=>{
    if (message.type === "GET_REQUESTS") {
        (0, _storage.StorageManager).getRequests().then((requests)=>{
            sendResponse({
                requests
            });
        });
        return true; // Keep message channel open for async response
    }
    if (message.type === "CLEAR_REQUESTS") {
        (0, _storage.StorageManager).clearRequests().then(()=>{
            sendResponse({
                success: true
            });
        });
        return true;
    }
    if (message.type === "EXPORT_REQUESTS") {
        (0, _storage.StorageManager).exportRequests().then((data)=>{
            sendResponse({
                data
            });
        }).catch((error)=>{
            sendResponse({
                error: error.message
            });
        });
        return true;
    }
    if (message.type === "IMPORT_REQUESTS") {
        (0, _storage.StorageManager).importRequests(message.data).then((count)=>{
            sendResponse({
                success: true,
                importedCount: count
            });
        }).catch((error)=>{
            sendResponse({
                error: error.message
            });
        });
        return true;
    }
    if (message.type === "GET_STORAGE_STATS") {
        (0, _storage.StorageManager).getStorageStats().then((stats)=>{
            sendResponse({
                stats
            });
        });
        return true;
    }
    // Handle capture status
    if (message.type === "GET_CAPTURE_STATUS") {
        sendResponse({
            isCapturing
        });
        return true;
    }
    if (message.type === "SET_CAPTURE_STATUS") {
        isCapturing = message.isCapturing;
        sendResponse({
            success: true
        });
        return true;
    }
    // Handle intercepted requests from content script
    if (message.type === "INTERCEPTED_REQUEST") {
        const interceptedData = message.data;
        // Convert to our RequestData format
        const requestData = {
            id: interceptedData.id,
            url: interceptedData.url,
            method: interceptedData.method,
            timestamp: interceptedData.timestamp,
            requestHeaders: Object.entries(interceptedData.requestHeaders || {}).map(([name, value])=>({
                    name,
                    value
                })),
            requestBody: interceptedData.requestBody,
            responseHeaders: Object.entries(interceptedData.responseHeaders || {}).map(([name, value])=>({
                    name,
                    value
                })),
            responseBody: interceptedData.responseBody,
            status: interceptedData.status,
            statusText: interceptedData.statusText,
            type: "fetch",
            tabId: sender.tab?.id || -1,
            duration: interceptedData.status ? Date.now() - interceptedData.timestamp : undefined
        };
        // Store the intercepted request
        storeRequestData(requestData);
        sendResponse({
            success: true
        });
        return true;
    }
});
console.log("API Interceptor background script loaded");

},{"./utils/storage":"6E8Wy"}],"6E8Wy":[function(require,module,exports) {
// Storage utility functions for API Interceptor Extension
var parcelHelpers = require("@parcel/transformer-js/src/esmodule-helpers.js");
parcelHelpers.defineInteropFlag(exports);
parcelHelpers.export(exports, "StorageManager", ()=>StorageManager);
class StorageManager {
    static #_ = (()=>{
        this.STORAGE_KEY = "apiRequests";
    })();
    static #_1 = (()=>{
        this.MAX_REQUESTS = 1000;
    })();
    static #_2 = (()=>{
        this.SETTINGS_KEY = "apiInterceptorSettings";
    })();
    // Get all stored requests
    static async getRequests() {
        try {
            const result = await chrome.storage.local.get([
                this.STORAGE_KEY
            ]);
            return result[this.STORAGE_KEY] || [];
        } catch (error) {
            console.error("Error getting requests from storage:", error);
            return [];
        }
    }
    // Store a new request
    static async storeRequest(request) {
        try {
            const existingRequests = await this.getRequests();
            const updatedRequests = [
                request,
                ...existingRequests
            ].slice(0, this.MAX_REQUESTS);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: updatedRequests
            });
        } catch (error) {
            console.error("Error storing request:", error);
        }
    }
    // Clear all requests
    static async clearRequests() {
        try {
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: []
            });
        } catch (error) {
            console.error("Error clearing requests:", error);
        }
    }
    // Get storage usage statistics
    static async getStorageStats() {
        try {
            const storageInfo = await chrome.storage.local.getBytesInUse();
            const quota = chrome.storage.local.QUOTA_BYTES;
            return {
                used: storageInfo,
                total: quota
            };
        } catch (error) {
            console.error("Error getting storage stats:", error);
            return {
                used: 0,
                total: 5242880 // 5MB default quota
            };
        }
    }
    // Export requests to JSON
    static async exportRequests() {
        try {
            const requests = await this.getRequests();
            return {
                version: "1.0",
                timestamp: new Date().toISOString(),
                requestCount: requests.length,
                requests: requests
            };
        } catch (error) {
            console.error("Error exporting requests:", error);
            throw error;
        }
    }
    // Import requests from JSON
    static async importRequests(importData) {
        try {
            if (!importData.requests || !Array.isArray(importData.requests)) throw new Error("Invalid import data format");
            const existingRequests = await this.getRequests();
            const importedRequests = importData.requests;
            // Merge and deduplicate requests
            const allRequests = [
                ...importedRequests,
                ...existingRequests
            ];
            const uniqueRequests = allRequests.filter((request, index, self)=>index === self.findIndex((r)=>r.id === request.id)).slice(0, this.MAX_REQUESTS);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: uniqueRequests
            });
            return importedRequests.length;
        } catch (error) {
            console.error("Error importing requests:", error);
            throw error;
        }
    }
    // Get settings
    static async getSettings() {
        try {
            const result = await chrome.storage.local.get([
                this.SETTINGS_KEY
            ]);
            return {
                maxRequests: this.MAX_REQUESTS,
                autoCapture: true,
                captureResponseBody: false,
                filterDomains: [],
                ...result[this.SETTINGS_KEY]
            };
        } catch (error) {
            console.error("Error getting settings:", error);
            return {
                maxRequests: this.MAX_REQUESTS,
                autoCapture: true,
                captureResponseBody: false,
                filterDomains: []
            };
        }
    }
    // Save settings
    static async saveSettings(settings) {
        try {
            const currentSettings = await this.getSettings();
            const updatedSettings = {
                ...currentSettings,
                ...settings
            };
            await chrome.storage.local.set({
                [this.SETTINGS_KEY]: updatedSettings
            });
        } catch (error) {
            console.error("Error saving settings:", error);
        }
    }
    // Clean old requests (older than specified days)
    static async cleanOldRequests(daysOld = 7) {
        try {
            const requests = await this.getRequests();
            const cutoffTime = Date.now() - daysOld * 86400000;
            const filteredRequests = requests.filter((request)=>request.timestamp > cutoffTime);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: filteredRequests
            });
            return requests.length - filteredRequests.length;
        } catch (error) {
            console.error("Error cleaning old requests:", error);
            return 0;
        }
    }
    // Search requests
    static async searchRequests(query) {
        try {
            const requests = await this.getRequests();
            const searchTerm = query.toLowerCase();
            return requests.filter((request)=>request.url.toLowerCase().includes(searchTerm) || request.method.toLowerCase().includes(searchTerm) || (request.status?.toString() || "").includes(searchTerm) || request.type.toLowerCase().includes(searchTerm));
        } catch (error) {
            console.error("Error searching requests:", error);
            return [];
        }
    }
    // Get requests by domain
    static async getRequestsByDomain(domain) {
        try {
            const requests = await this.getRequests();
            return requests.filter((request)=>{
                try {
                    const url = new URL(request.url);
                    return url.hostname === domain;
                } catch  {
                    return false;
                }
            });
        } catch (error) {
            console.error("Error getting requests by domain:", error);
            return [];
        }
    }
    // Get unique domains from stored requests
    static async getUniqueDomains() {
        try {
            const requests = await this.getRequests();
            const domains = new Set();
            requests.forEach((request)=>{
                try {
                    const url = new URL(request.url);
                    domains.add(url.hostname);
                } catch  {
                // Ignore invalid URLs
                }
            });
            return Array.from(domains).sort();
        } catch (error) {
            console.error("Error getting unique domains:", error);
            return [];
        }
    }
}

},{"@parcel/transformer-js/src/esmodule-helpers.js":"5G9Z5"}],"5G9Z5":[function(require,module,exports) {
exports.interopDefault = function(a) {
    return a && a.__esModule ? a : {
        default: a
    };
};
exports.defineInteropFlag = function(a) {
    Object.defineProperty(a, "__esModule", {
        value: true
    });
};
exports.exportAll = function(source, dest) {
    Object.keys(source).forEach(function(key) {
        if (key === "default" || key === "__esModule" || dest.hasOwnProperty(key)) return;
        Object.defineProperty(dest, key, {
            enumerable: true,
            get: function() {
                return source[key];
            }
        });
    });
    return dest;
};
exports.export = function(dest, destName, get) {
    Object.defineProperty(dest, destName, {
        enumerable: true,
        get: get
    });
};

},{}]},["l2tdl","8oeFb"], "8oeFb", "parcelRequireff35")

//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
 globalThis.define=__define;  })(globalThis.define);