{"icons": {"16": "icon16.plasmo.9f44d99c.png", "32": "icon32.plasmo.83dbbbab.png", "48": "icon48.plasmo.a78c509e.png", "64": "icon64.plasmo.15206795.png", "128": "icon128.plasmo.c11f39af.png"}, "manifest_version": 3, "action": {"default_icon": {"16": "icon16.plasmo.9f44d99c.png", "32": "icon32.plasmo.83dbbbab.png", "48": "icon48.plasmo.a78c509e.png", "64": "icon64.plasmo.15206795.png", "128": "icon128.plasmo.c11f39af.png"}, "default_popup": "popup.html"}, "version": "0.0.1", "author": "Plasmo Corp. <<EMAIL>>", "name": "DEV | Api interceptor extension", "description": "A Chrome extension to intercept and display all API requests with beautiful UI", "background": {"service_worker": "static/background/index.js"}, "options_ui": {"page": "options.html", "open_in_tab": true}, "permissions": ["webRequest", "storage", "activeTab"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.672cac6b.js"], "css": []}], "host_permissions": ["https://*/*", "http://*/*"], "options_page": "options.html", "content_security_policy": {"extension_pages": "script-src 'self' http://localhost;object-src 'self';"}, "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["__plasmo_hmr_proxy__"]}]}