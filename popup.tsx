import React from "react";
import "./style.css";

function IndexPopup() {

  // Load requests from storage
  const loadRequests = async () => {
    setIsLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
      const requestData = response.requests || [];
      setRequests(requestData);
      setFilteredRequests(requestData);
    } catch (error) {
      console.error('Error loading requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all requests
  const clearRequests = async () => {
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setRequests([]);
      setFilteredRequests([]);
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error clearing requests:', error);
    }
  };

  // Filter requests based on search and filters
  const handleFilterChange = (filter: RequestFilter & { searchTerm: string }) => {
    let filtered = [...requests];

    // Search term filter
    if (filter.searchTerm) {
      const searchLower = filter.searchTerm.toLowerCase();
      filtered = filtered.filter(request =>
        request.url.toLowerCase().includes(searchLower) ||
        request.method.toLowerCase().includes(searchLower) ||
        (request.status?.toString() || '').includes(searchLower)
      );
    }

    // Method filter
    if (filter.method) {
      filtered = filtered.filter(request =>
        request.method.toLowerCase() === filter.method?.toLowerCase()
      );
    }

    // Status filter
    if (filter.status) {
      if (filter.status === 'failed') {
        filtered = filtered.filter(request => !request.status || request.status === 0);
      } else if (filter.status.endsWith('xx')) {
        const statusPrefix = filter.status.charAt(0);
        filtered = filtered.filter(request =>
          request.status && request.status.toString().startsWith(statusPrefix)
        );
      }
    }

    // Type filter
    if (filter.type) {
      filtered = filtered.filter(request =>
        request.type.toLowerCase() === filter.type?.toLowerCase()
      );
    }

    setFilteredRequests(filtered);
  };

  // Handle request selection
  const handleRequestSelect = (request: RequestData) => {
    setSelectedRequest(request);
    setShowDetails(true);
  };

  const handleBackToList = () => {
    setShowDetails(false);
    setSelectedRequest(null);
  };

  const handleShowSettings = () => {
    setShowSettings(true);
  };

  const handleBackFromSettings = () => {
    setShowSettings(false);
  };

  const handleDataChange = () => {
    loadRequests();
  };

  // Listen for new requests
  useEffect(() => {
    loadRequests();

    const handleMessage = (message: any) => {
      if (message.type === 'NEW_REQUEST') {
        setRequests(prev => [message.data, ...prev]);
        setFilteredRequests(prev => [message.data, ...prev]);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    return () => chrome.runtime.onMessage.removeListener(handleMessage);
  }, []);

  // Show settings if settings is selected
  if (showSettings) {
    return (
      <Settings
        onBack={handleBackFromSettings}
        onDataChange={handleDataChange}
      />
    );
  }

  // Show request details if a request is selected
  if (showDetails && selectedRequest) {
    return (
      <RequestDetails
        request={selectedRequest}
        onBack={handleBackToList}
      />
    );
  }

  return (
    <div className="w-[480px] h-[600px] bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-bold text-gray-900">API Interceptor</h1>
          <div className="flex items-center gap-2">
            <button
              onClick={loadRequests}
              className="btn btn-ghost btn-sm"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={handleShowSettings}
              className="btn btn-ghost btn-sm"
            >
              <SettingsIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        <RequestStats requests={filteredRequests} />

        <SearchFilter
          onFilterChange={handleFilterChange}
          onClear={clearRequests}
          requestCount={filteredRequests.length}
        />
      </div>

      {/* Request List */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
          </div>
        ) : (
          <div className="h-full overflow-y-auto scrollbar-thin p-4">
            <RequestList
              requests={filteredRequests}
              onRequestSelect={handleRequestSelect}
              selectedRequestId={selectedRequest?.id}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default IndexPopup;
