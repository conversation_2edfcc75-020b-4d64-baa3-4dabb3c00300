import React, { useState, useEffect } from "react";

interface RequestData {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  status?: number;
  duration?: number;
}

function IndexPopup() {
  const [requests, setRequests] = useState<RequestData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load requests from storage
  const loadRequests = async () => {
    setIsLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
      const requestData = response.requests || [];
      setRequests(requestData);
    } catch (error) {
      console.error('Error loading requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all requests
  const clearRequests = async () => {
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setRequests([]);
    } catch (error) {
      console.error('Error clearing requests:', error);
    }
  };

  // Listen for new requests
  useEffect(() => {
    loadRequests();

    const handleMessage = (message: any) => {
      if (message.type === 'NEW_REQUEST') {
        setRequests(prev => [message.data, ...prev]);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    return () => chrome.runtime.onMessage.removeListener(handleMessage);
  }, []);

  return (
    <div style={{ width: '480px', height: '600px', backgroundColor: '#f9fafb', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
          <h1 style={{ fontSize: '18px', fontWeight: 'bold', color: '#111827', margin: 0 }}>API Interceptor</h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <button
              onClick={loadRequests}
              disabled={isLoading}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: isLoading ? 'not-allowed' : 'pointer'
              }}
            >
              {isLoading ? '⟳' : '↻'} Refresh
            </button>
            <button
              onClick={clearRequests}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#fef2f2',
                color: '#b91c1c',
                border: '1px solid #fecaca',
                borderRadius: '6px',
                cursor: 'pointer'
              }}
            >
              Clear
            </button>
          </div>
        </div>

        {/* Stats */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px', marginBottom: '16px' }}>
          <div style={{ backgroundColor: '#eff6ff', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>{requests.length}</div>
            <div style={{ fontSize: '12px', color: '#2563eb' }}>Total</div>
          </div>
          <div style={{ backgroundColor: '#f0fdf4', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#16a34a' }}>
              {requests.filter(r => r.status && r.status >= 200 && r.status < 400).length}
            </div>
            <div style={{ fontSize: '12px', color: '#16a34a' }}>Success</div>
          </div>
          <div style={{ backgroundColor: '#fef2f2', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#dc2626' }}>
              {requests.filter(r => !r.status || r.status >= 400).length}
            </div>
            <div style={{ fontSize: '12px', color: '#dc2626' }}>Errors</div>
          </div>
        </div>
      </div>

      {/* Request List */}
      <div style={{ flex: 1, overflowY: 'auto', padding: '16px' }}>
        {isLoading ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <div style={{ fontSize: '24px' }}>⟳</div>
          </div>
        ) : requests.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#6b7280', marginTop: '32px' }}>
            <p>No requests captured yet</p>
            <p style={{ fontSize: '14px', marginTop: '4px' }}>Browse any website to see requests</p>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {requests.slice(0, 50).map((request) => (
              <div
                key={request.id}
                style={{
                  backgroundColor: 'white',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      padding: '2px 8px',
                      fontSize: '12px',
                      fontWeight: '500',
                      borderRadius: '4px',
                      backgroundColor: request.method === 'GET' ? '#dcfce7' :
                                     request.method === 'POST' ? '#dbeafe' :
                                     request.method === 'PUT' ? '#fef3c7' :
                                     request.method === 'DELETE' ? '#fecaca' : '#f3f4f6',
                      color: request.method === 'GET' ? '#166534' :
                             request.method === 'POST' ? '#1e40af' :
                             request.method === 'PUT' ? '#92400e' :
                             request.method === 'DELETE' ? '#991b1b' : '#374151'
                    }}>
                      {request.method}
                    </span>
                    <span style={{
                      padding: '2px 8px',
                      fontSize: '12px',
                      fontWeight: '500',
                      borderRadius: '4px',
                      backgroundColor: request.status && request.status >= 200 && request.status < 300 ? '#dcfce7' :
                                     request.status && request.status >= 300 && request.status < 400 ? '#fef3c7' :
                                     request.status && request.status >= 400 ? '#fecaca' : '#f3f4f6',
                      color: request.status && request.status >= 200 && request.status < 300 ? '#166534' :
                             request.status && request.status >= 300 && request.status < 400 ? '#92400e' :
                             request.status && request.status >= 400 ? '#991b1b' : '#374151'
                    }}>
                      {request.status || 'ERR'}
                    </span>
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>
                    {request.duration ? `${request.duration}ms` : '-'}
                  </div>
                </div>
                <div style={{
                  fontSize: '14px',
                  fontFamily: 'monospace',
                  color: '#111827',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {request.url}
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                  {new Date(request.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default IndexPopup;
