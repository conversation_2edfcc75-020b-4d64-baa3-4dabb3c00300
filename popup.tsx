import React, { useState, useEffect } from "react";
import { RefreshCw, Settings as SettingsIcon } from "lucide-react";
import type { RequestData } from "./types";
import "./style.css";

function IndexPopup() {
  const [requests, setRequests] = useState<RequestData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load requests from storage
  const loadRequests = async () => {
    setIsLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
      const requestData = response.requests || [];
      setRequests(requestData);
    } catch (error) {
      console.error('Error loading requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all requests
  const clearRequests = async () => {
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setRequests([]);
    } catch (error) {
      console.error('Error clearing requests:', error);
    }
  };

  // Listen for new requests
  useEffect(() => {
    loadRequests();

    const handleMessage = (message: any) => {
      if (message.type === 'NEW_REQUEST') {
        setRequests(prev => [message.data, ...prev]);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    return () => chrome.runtime.onMessage.removeListener(handleMessage);
  }, []);

  return (
    <div className="w-[480px] h-[600px] bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-bold text-gray-900">API Interceptor</h1>
          <div className="flex items-center gap-2">
            <button
              onClick={loadRequests}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded flex items-center gap-2"
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={clearRequests}
              className="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded"
            >
              Clear
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-lg font-bold text-blue-600">{requests.length}</div>
            <div className="text-xs text-blue-600">Total</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-lg font-bold text-green-600">
              {requests.filter(r => r.status && r.status >= 200 && r.status < 400).length}
            </div>
            <div className="text-xs text-green-600">Success</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg text-center">
            <div className="text-lg font-bold text-red-600">
              {requests.filter(r => !r.status || r.status >= 400).length}
            </div>
            <div className="text-xs text-red-600">Errors</div>
          </div>
        </div>
      </div>

      {/* Request List */}
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
          </div>
        ) : requests.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p>No requests captured yet</p>
            <p className="text-sm mt-1">Browse any website to see requests</p>
          </div>
        ) : (
          <div className="space-y-2">
            {requests.slice(0, 50).map((request) => (
              <div key={request.id} className="bg-white p-3 rounded-lg border border-gray-200 hover:border-gray-300">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded ${
                      request.method === 'GET' ? 'bg-green-100 text-green-800' :
                      request.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                      request.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                      request.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {request.method}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded ${
                      request.status && request.status >= 200 && request.status < 300 ? 'bg-green-100 text-green-800' :
                      request.status && request.status >= 300 && request.status < 400 ? 'bg-yellow-100 text-yellow-800' :
                      request.status && request.status >= 400 ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {request.status || 'ERR'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {request.duration ? `${request.duration}ms` : '-'}
                  </div>
                </div>
                <div className="text-sm font-mono text-gray-900 truncate">
                  {request.url}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {new Date(request.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default IndexPopup;
