import React, { useState, useEffect } from "react";

interface RequestData {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  status?: number;
  duration?: number;
}

function IndexPopup() {
  const [requests, setRequests] = useState<RequestData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<RequestData | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredRequests, setFilteredRequests] = useState<RequestData[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [isCapturing, setIsCapturing] = useState<boolean>(true);
  const [isReplayingRequest, setIsReplayingRequest] = useState<boolean>(false);
  const [replayResponse, setReplayResponse] = useState<string | null>(null);

  // Load requests from storage
  const loadRequests = async () => {
    setIsLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
      const requestData = response.requests || [];
      setRequests(requestData);
      setFilteredRequests(filterRequests(requestData, searchTerm, selectedMethod));

      // Also get capturing status
      const statusResponse = await chrome.runtime.sendMessage({ type: 'GET_CAPTURE_STATUS' });
      setIsCapturing(statusResponse.isCapturing !== false); // Default to true
    } catch (error) {
      console.error('Error loading requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all requests
  const clearRequests = async () => {
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setRequests([]);
      setFilteredRequests([]);
    } catch (error) {
      console.error('Error clearing requests:', error);
    }
  };

  // Handle request selection
  const handleRequestClick = (request: RequestData) => {
    setSelectedRequest(request);
    setShowDetails(true);
    setReplayResponse(null); // Reset replay response
  };

  const handleBackToList = () => {
    setShowDetails(false);
    setSelectedRequest(null);
    setReplayResponse(null);
  };

  // Filter requests based on search term and method
  const filterRequests = (requestList: RequestData[], search: string, method: string) => {
    let filtered = requestList;

    // Filter by method if selected
    if (method) {
      filtered = filtered.filter(request => request.method === method);
    }

    // Filter by search term if provided
    if (search.trim()) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(request =>
        request.url.toLowerCase().includes(searchLower) ||
        request.method.toLowerCase().includes(searchLower) ||
        (request.status?.toString() || '').includes(searchLower)
      );
    }

    return filtered;
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setFilteredRequests(filterRequests(requests, value, selectedMethod));
  };

  // Handle method filter change
  const handleMethodChange = (method: string) => {
    setSelectedMethod(method);
    setFilteredRequests(filterRequests(requests, searchTerm, method));
  };

  // Toggle capturing
  const toggleCapturing = async () => {
    try {
      const newStatus = !isCapturing;
      await chrome.runtime.sendMessage({
        type: 'SET_CAPTURE_STATUS',
        isCapturing: newStatus
      });
      setIsCapturing(newStatus);
    } catch (error) {
      console.error('Error toggling capture:', error);
    }
  };

  // Export requests as JSON
  const exportRequests = () => {
    const dataToExport = filteredRequests.length > 0 ? filteredRequests : requests;
    const dataStr = JSON.stringify(dataToExport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `api-requests-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Open options page
  const openOptionsPage = () => {
    chrome.runtime.openOptionsPage();
  };

  // Replay request to get response body
  const replayRequest = async (request: RequestData) => {
    setIsReplayingRequest(true);
    setReplayResponse(null);

    try {
      // Reconstruct the request
      const headers: Record<string, string> = {};
      request.requestHeaders?.forEach(header => {
        // Skip some headers that browsers handle automatically
        const skipHeaders = ['host', 'user-agent', 'accept-encoding', 'connection', 'upgrade-insecure-requests'];
        if (!skipHeaders.includes(header.name.toLowerCase())) {
          headers[header.name] = header.value || '';
        }
      });

      const fetchOptions: RequestInit = {
        method: request.method,
        headers: headers,
        mode: 'cors', // Allow cross-origin requests
        credentials: 'omit' // Don't send credentials to avoid CORS issues
      };

      // Add body for POST/PUT/PATCH requests
      if (request.requestBody && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        fetchOptions.body = request.requestBody;
      }

      console.log('Replaying request:', request.url, fetchOptions);

      const response = await fetch(request.url, fetchOptions);

      // Try to get response body
      const contentType = response.headers.get('content-type') || '';
      let responseBody: string;

      if (contentType.includes('application/json')) {
        const jsonData = await response.json();
        responseBody = JSON.stringify(jsonData, null, 2);
      } else if (contentType.includes('text/') || contentType.includes('application/xml')) {
        responseBody = await response.text();
      } else {
        responseBody = `[${contentType || 'Binary Data'}] - ${response.headers.get('content-length') || 'Unknown'} bytes`;
      }

      setReplayResponse(responseBody);
    } catch (error) {
      console.error('Error replaying request:', error);
      setReplayResponse(`Error: ${error instanceof Error ? error.message : 'Unknown error'}\n\nThis might be due to:\n- CORS restrictions\n- Network connectivity\n- Server authentication requirements\n- Invalid request parameters`);
    } finally {
      setIsReplayingRequest(false);
    }
  };

  // Listen for new requests
  useEffect(() => {
    loadRequests();

    const handleMessage = (message: any) => {
      if (message.type === 'NEW_REQUEST') {
        const newRequests = [message.data, ...requests];
        setRequests(newRequests);
        setFilteredRequests(filterRequests(newRequests, searchTerm, selectedMethod));
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    return () => chrome.runtime.onMessage.removeListener(handleMessage);
  }, []);

  // Show request details if selected
  if (showDetails && selectedRequest) {
    return (
      <div style={{ width: '480px', height: '600px', backgroundColor: 'white', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
            <button
              onClick={handleBackToList}
              style={{
                padding: '8px',
                backgroundColor: '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: 'pointer'
              }}
            >
              ← Back
            </button>
            <h1 style={{ fontSize: '18px', fontWeight: 'bold', color: '#111827', margin: 0 }}>Request Details</h1>
          </div>

          {/* Request Summary */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <span style={{
              padding: '4px 8px',
              fontSize: '12px',
              fontWeight: '500',
              borderRadius: '4px',
              backgroundColor: selectedRequest.method === 'GET' ? '#dcfce7' :
                             selectedRequest.method === 'POST' ? '#dbeafe' :
                             selectedRequest.method === 'PUT' ? '#fef3c7' :
                             selectedRequest.method === 'DELETE' ? '#fecaca' : '#f3f4f6',
              color: selectedRequest.method === 'GET' ? '#166534' :
                     selectedRequest.method === 'POST' ? '#1e40af' :
                     selectedRequest.method === 'PUT' ? '#92400e' :
                     selectedRequest.method === 'DELETE' ? '#991b1b' : '#374151'
            }}>
              {selectedRequest.method}
            </span>
            <span style={{
              padding: '4px 8px',
              fontSize: '12px',
              fontWeight: '500',
              borderRadius: '4px',
              backgroundColor: selectedRequest.status && selectedRequest.status >= 200 && selectedRequest.status < 300 ? '#dcfce7' :
                             selectedRequest.status && selectedRequest.status >= 300 && selectedRequest.status < 400 ? '#fef3c7' :
                             selectedRequest.status && selectedRequest.status >= 400 ? '#fecaca' : '#f3f4f6',
              color: selectedRequest.status && selectedRequest.status >= 200 && selectedRequest.status < 300 ? '#166534' :
                     selectedRequest.status && selectedRequest.status >= 300 && selectedRequest.status < 400 ? '#92400e' :
                     selectedRequest.status && selectedRequest.status >= 400 ? '#991b1b' : '#374151'
            }}>
              {selectedRequest.status || 'ERR'}
            </span>
            <span style={{ fontSize: '12px', color: '#6b7280' }}>
              {selectedRequest.duration ? `${selectedRequest.duration}ms` : '-'}
            </span>
          </div>

          <div style={{
            fontSize: '14px',
            fontFamily: 'monospace',
            color: '#111827',
            wordBreak: 'break-all',
            marginBottom: '8px'
          }}>
            {selectedRequest.url}
          </div>

          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            {new Date(selectedRequest.timestamp).toLocaleString()}
          </div>
        </div>

        {/* Request Details Content */}
        <div style={{ flex: 1, overflowY: 'auto', padding: '16px' }}>
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
              Request Information
            </h3>
            <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '8px', fontSize: '14px' }}>
                <div style={{ fontWeight: '500', color: '#374151' }}>Method:</div>
                <div style={{ fontFamily: 'monospace', color: '#111827' }}>{selectedRequest.method}</div>

                <div style={{ fontWeight: '500', color: '#374151' }}>Status:</div>
                <div style={{ fontFamily: 'monospace', color: '#111827' }}>{selectedRequest.status || 'Failed'}</div>

                <div style={{ fontWeight: '500', color: '#374151' }}>Duration:</div>
                <div style={{ fontFamily: 'monospace', color: '#111827' }}>{selectedRequest.duration ? `${selectedRequest.duration}ms` : 'N/A'}</div>

                <div style={{ fontWeight: '500', color: '#374151' }}>Timestamp:</div>
                <div style={{ fontFamily: 'monospace', color: '#111827' }}>{new Date(selectedRequest.timestamp).toISOString()}</div>
              </div>
            </div>
          </div>

          <div style={{ marginBottom: '24px' }}>
            <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
              URL Details
            </h3>
            <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px' }}>
              <div style={{
                fontSize: '14px',
                fontFamily: 'monospace',
                color: '#111827',
                wordBreak: 'break-all',
                lineHeight: '1.5'
              }}>
                {selectedRequest.url}
              </div>
            </div>
          </div>

          {/* Request Headers */}
          {selectedRequest.requestHeaders && selectedRequest.requestHeaders.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
                Request Headers
              </h3>
              <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px', maxHeight: '200px', overflowY: 'auto' }}>
                {selectedRequest.requestHeaders.map((header, index) => (
                  <div key={index} style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '8px', marginBottom: '4px', fontSize: '14px' }}>
                    <div style={{ fontWeight: '500', color: '#374151', fontFamily: 'monospace' }}>{header.name}:</div>
                    <div style={{ color: '#111827', fontFamily: 'monospace', wordBreak: 'break-all' }}>{header.value}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Request Body */}
          {selectedRequest.requestBody && (
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
                Request Body
              </h3>
              <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px', maxHeight: '200px', overflowY: 'auto' }}>
                <pre style={{
                  fontSize: '14px',
                  fontFamily: 'monospace',
                  color: '#111827',
                  margin: 0,
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all'
                }}>
                  {selectedRequest.requestBody}
                </pre>
              </div>
            </div>
          )}

          {/* Response Headers */}
          {selectedRequest.responseHeaders && selectedRequest.responseHeaders.length > 0 && (
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', marginBottom: '12px' }}>
                Response Headers
              </h3>
              <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px', maxHeight: '200px', overflowY: 'auto' }}>
                {selectedRequest.responseHeaders.map((header, index) => (
                  <div key={index} style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '8px', marginBottom: '4px', fontSize: '14px' }}>
                    <div style={{ fontWeight: '500', color: '#374151', fontFamily: 'monospace' }}>{header.name}:</div>
                    <div style={{ color: '#111827', fontFamily: 'monospace', wordBreak: 'break-all' }}>{header.value}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Response Body */}
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: 0 }}>
                Response Body
              </h3>
              <button
                onClick={() => replayRequest(selectedRequest)}
                disabled={isReplayingRequest}
                style={{
                  padding: '6px 12px',
                  fontSize: '12px',
                  backgroundColor: isReplayingRequest ? '#f3f4f6' : '#f0f9ff',
                  border: '1px solid ' + (isReplayingRequest ? '#d1d5db' : '#bae6fd'),
                  borderRadius: '4px',
                  cursor: isReplayingRequest ? 'not-allowed' : 'pointer',
                  color: isReplayingRequest ? '#6b7280' : '#0369a1',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                {isReplayingRequest ? '⟳' : '🔄'} {isReplayingRequest ? 'Replaying...' : 'Replay Request'}
              </button>
            </div>

            {/* Show original response body if available */}
            {selectedRequest.responseBody && (
              <div style={{ marginBottom: '16px' }}>
                <h4 style={{ fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                  Original Response (from interception):
                </h4>
                <div style={{ backgroundColor: '#f9fafb', padding: '12px', borderRadius: '8px', maxHeight: '200px', overflowY: 'auto' }}>
                  <pre style={{
                    fontSize: '14px',
                    fontFamily: 'monospace',
                    color: '#111827',
                    margin: 0,
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all'
                  }}>
                    {(() => {
                      try {
                        // Try to format JSON
                        const parsed = JSON.parse(selectedRequest.responseBody);
                        return JSON.stringify(parsed, null, 2);
                      } catch {
                        // Return as-is if not JSON
                        return selectedRequest.responseBody;
                      }
                    })()}
                  </pre>
                </div>
              </div>
            )}

            {/* Show replayed response */}
            {replayResponse && (
              <div style={{ marginBottom: '16px' }}>
                <h4 style={{ fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                  Replayed Response:
                </h4>
                <div style={{ backgroundColor: '#f0fdf4', padding: '12px', borderRadius: '8px', maxHeight: '300px', overflowY: 'auto', border: '1px solid #86efac' }}>
                  <pre style={{
                    fontSize: '14px',
                    fontFamily: 'monospace',
                    color: '#111827',
                    margin: 0,
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all'
                  }}>
                    {replayResponse}
                  </pre>
                </div>
              </div>
            )}

            {/* Show warning if no response body available */}
            {!selectedRequest.responseBody && !replayResponse && !isReplayingRequest && (
              <div style={{
                backgroundColor: '#fef3c7',
                padding: '12px',
                borderRadius: '8px',
                border: '1px solid #fbbf24'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                  <span style={{ fontSize: '16px' }}>⚠️</span>
                  <span style={{ fontSize: '14px', fontWeight: '500', color: '#92400e' }}>
                    Response Body Not Available
                  </span>
                </div>
                <p style={{ fontSize: '14px', color: '#92400e', margin: '0 0 8px 0', lineHeight: '1.4' }}>
                  Response body capture is currently limited by browser security policies.
                </p>
                <p style={{ fontSize: '14px', color: '#92400e', margin: '0 0 8px 0', lineHeight: '1.4' }}>
                  <strong>Try the "Replay Request" button above</strong> to re-execute this request and capture the response body.
                </p>
                <p style={{ fontSize: '12px', color: '#92400e', margin: 0, lineHeight: '1.4' }}>
                  Note: Replayed requests may fail due to CORS restrictions, authentication requirements, or changed server state.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ width: '480px', height: '600px', backgroundColor: '#f9fafb', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <h1 style={{ fontSize: '18px', fontWeight: 'bold', color: '#111827', margin: 0 }}>API Interceptor</h1>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: isCapturing ? '#10b981' : '#ef4444'
              }} />
              <span style={{ fontSize: '12px', color: '#6b7280' }}>
                {isCapturing ? 'Capturing' : 'Stopped'}
              </span>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <button
              onClick={toggleCapturing}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: isCapturing ? '#fef2f2' : '#f0fdf4',
                border: '1px solid ' + (isCapturing ? '#fecaca' : '#86efac'),
                borderRadius: '6px',
                cursor: 'pointer',
                color: isCapturing ? '#b91c1c' : '#166534'
              }}
            >
              {isCapturing ? '⏸ Stop' : '▶ Start'}
            </button>
            <button
              onClick={loadRequests}
              disabled={isLoading}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: isLoading ? 'not-allowed' : 'pointer'
              }}
            >
              {isLoading ? '⟳' : '↻'}
            </button>
            <button
              onClick={exportRequests}
              disabled={requests.length === 0}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#f0f9ff',
                border: '1px solid #bae6fd',
                borderRadius: '6px',
                cursor: requests.length === 0 ? 'not-allowed' : 'pointer',
                color: '#0369a1',
                opacity: requests.length === 0 ? 0.5 : 1
              }}
            >
              ⬇
            </button>
            <button
              onClick={openOptionsPage}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#f3f4f6',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                cursor: 'pointer',
                color: '#374151'
              }}
            >
              ⚙️
            </button>
            <button
              onClick={clearRequests}
              style={{
                padding: '4px 12px',
                fontSize: '14px',
                backgroundColor: '#fef2f2',
                color: '#b91c1c',
                border: '1px solid #fecaca',
                borderRadius: '6px',
                cursor: 'pointer'
              }}
            >
              🗑
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div style={{ marginBottom: '16px' }}>
          <input
            type="text"
            placeholder="Search requests by URL, method, or status..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              fontSize: '14px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              outline: 'none',
              boxSizing: 'border-box',
              marginBottom: '8px'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2563eb'}
            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
          />

          {/* Method Filter */}
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {['', 'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'].map((method) => (
              <button
                key={method}
                onClick={() => handleMethodChange(method)}
                style={{
                  padding: '4px 12px',
                  fontSize: '12px',
                  fontWeight: '500',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  backgroundColor: selectedMethod === method ? '#2563eb' : 'white',
                  color: selectedMethod === method ? 'white' : '#374151',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (selectedMethod !== method) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedMethod !== method) {
                    e.currentTarget.style.backgroundColor = 'white';
                  }
                }}
              >
                {method || 'All'}
              </button>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px', marginBottom: '16px' }}>
          <div style={{ backgroundColor: '#eff6ff', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>{filteredRequests.length}</div>
            <div style={{ fontSize: '12px', color: '#2563eb' }}>Showing</div>
          </div>
          <div style={{ backgroundColor: '#f0fdf4', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#16a34a' }}>
              {filteredRequests.filter(r => r.status && r.status >= 200 && r.status < 400).length}
            </div>
            <div style={{ fontSize: '12px', color: '#16a34a' }}>Success</div>
          </div>
          <div style={{ backgroundColor: '#fef2f2', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#dc2626' }}>
              {filteredRequests.filter(r => !r.status || r.status >= 400).length}
            </div>
            <div style={{ fontSize: '12px', color: '#dc2626' }}>Errors</div>
          </div>
        </div>
      </div>

      {/* Request List */}
      <div style={{ flex: 1, overflowY: 'auto', padding: '16px' }}>
        {isLoading ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <div style={{ fontSize: '24px' }}>⟳</div>
          </div>
        ) : filteredRequests.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#6b7280', marginTop: '32px' }}>
            {requests.length === 0 ? (
              <>
                <p>No requests captured yet</p>
                <p style={{ fontSize: '14px', marginTop: '4px' }}>Browse any website to see requests</p>
              </>
            ) : (
              <>
                <p>No requests match your search</p>
                <p style={{ fontSize: '14px', marginTop: '4px' }}>Try a different search term</p>
              </>
            )}
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {filteredRequests.slice(0, 50).map((request) => (
              <div
                key={request.id}
                onClick={() => handleRequestClick(request)}
                style={{
                  backgroundColor: 'white',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#e5e7eb';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      padding: '2px 8px',
                      fontSize: '12px',
                      fontWeight: '500',
                      borderRadius: '4px',
                      backgroundColor: request.method === 'GET' ? '#dcfce7' :
                                     request.method === 'POST' ? '#dbeafe' :
                                     request.method === 'PUT' ? '#fef3c7' :
                                     request.method === 'DELETE' ? '#fecaca' : '#f3f4f6',
                      color: request.method === 'GET' ? '#166534' :
                             request.method === 'POST' ? '#1e40af' :
                             request.method === 'PUT' ? '#92400e' :
                             request.method === 'DELETE' ? '#991b1b' : '#374151'
                    }}>
                      {request.method}
                    </span>
                    <span style={{
                      padding: '2px 8px',
                      fontSize: '12px',
                      fontWeight: '500',
                      borderRadius: '4px',
                      backgroundColor: request.status && request.status >= 200 && request.status < 300 ? '#dcfce7' :
                                     request.status && request.status >= 300 && request.status < 400 ? '#fef3c7' :
                                     request.status && request.status >= 400 ? '#fecaca' : '#f3f4f6',
                      color: request.status && request.status >= 200 && request.status < 300 ? '#166534' :
                             request.status && request.status >= 300 && request.status < 400 ? '#92400e' :
                             request.status && request.status >= 400 ? '#991b1b' : '#374151'
                    }}>
                      {request.status || 'ERR'}
                    </span>
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>
                    {request.duration ? `${request.duration}ms` : '-'}
                  </div>
                </div>
                <div style={{
                  fontSize: '14px',
                  fontFamily: 'monospace',
                  color: '#111827',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {request.url}
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                  {new Date(request.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default IndexPopup;
