// Storage utility functions for API Interceptor Extension

import type { RequestData, StorageData } from '../types';

export class StorageManager {
  private static readonly STORAGE_KEY = 'apiRequests';
  private static readonly MAX_REQUESTS = 1000;
  private static readonly SETTINGS_KEY = 'apiInterceptorSettings';

  // Get all stored requests
  static async getRequests(): Promise<RequestData[]> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEY]);
      return result[this.STORAGE_KEY] || [];
    } catch (error) {
      console.error('Error getting requests from storage:', error);
      return [];
    }
  }

  // Store a new request
  static async storeRequest(request: RequestData): Promise<void> {
    try {
      const existingRequests = await this.getRequests();
      const updatedRequests = [request, ...existingRequests].slice(0, this.MAX_REQUESTS);
      
      await chrome.storage.local.set({
        [this.STORAGE_KEY]: updatedRequests
      });
    } catch (error) {
      console.error('Error storing request:', error);
    }
  }

  // Clear all requests
  static async clearRequests(): Promise<void> {
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEY]: []
      });
    } catch (error) {
      console.error('Error clearing requests:', error);
    }
  }

  // Get storage usage statistics
  static async getStorageStats(): Promise<{
    requestCount: number;
    storageUsed: number;
    storageQuota: number;
    usagePercentage: number;
  }> {
    try {
      const requests = await this.getRequests();
      const storageInfo = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      
      return {
        requestCount: requests.length,
        storageUsed: storageInfo,
        storageQuota: quota,
        usagePercentage: (storageInfo / quota) * 100
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        requestCount: 0,
        storageUsed: 0,
        storageQuota: 0,
        usagePercentage: 0
      };
    }
  }

  // Export requests to JSON
  static async exportRequests(): Promise<string> {
    try {
      const requests = await this.getRequests();
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        requestCount: requests.length,
        requests: requests
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting requests:', error);
      throw error;
    }
  }

  // Import requests from JSON
  static async importRequests(jsonData: string): Promise<number> {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.requests || !Array.isArray(importData.requests)) {
        throw new Error('Invalid import data format');
      }

      const existingRequests = await this.getRequests();
      const importedRequests = importData.requests as RequestData[];
      
      // Merge and deduplicate requests
      const allRequests = [...importedRequests, ...existingRequests];
      const uniqueRequests = allRequests.filter((request, index, self) =>
        index === self.findIndex(r => r.id === request.id)
      ).slice(0, this.MAX_REQUESTS);

      await chrome.storage.local.set({
        [this.STORAGE_KEY]: uniqueRequests
      });

      return importedRequests.length;
    } catch (error) {
      console.error('Error importing requests:', error);
      throw error;
    }
  }

  // Get settings
  static async getSettings(): Promise<{
    maxRequests: number;
    autoCapture: boolean;
    captureResponseBody: boolean;
    filterDomains: string[];
  }> {
    try {
      const result = await chrome.storage.local.get([this.SETTINGS_KEY]);
      return {
        maxRequests: this.MAX_REQUESTS,
        autoCapture: true,
        captureResponseBody: false,
        filterDomains: [],
        ...result[this.SETTINGS_KEY]
      };
    } catch (error) {
      console.error('Error getting settings:', error);
      return {
        maxRequests: this.MAX_REQUESTS,
        autoCapture: true,
        captureResponseBody: false,
        filterDomains: []
      };
    }
  }

  // Save settings
  static async saveSettings(settings: {
    maxRequests?: number;
    autoCapture?: boolean;
    captureResponseBody?: boolean;
    filterDomains?: string[];
  }): Promise<void> {
    try {
      const currentSettings = await this.getSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      
      await chrome.storage.local.set({
        [this.SETTINGS_KEY]: updatedSettings
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  // Clean old requests (older than specified days)
  static async cleanOldRequests(daysOld: number = 7): Promise<number> {
    try {
      const requests = await this.getRequests();
      const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
      
      const filteredRequests = requests.filter(request => 
        request.timestamp > cutoffTime
      );

      await chrome.storage.local.set({
        [this.STORAGE_KEY]: filteredRequests
      });

      return requests.length - filteredRequests.length;
    } catch (error) {
      console.error('Error cleaning old requests:', error);
      return 0;
    }
  }

  // Search requests
  static async searchRequests(query: string): Promise<RequestData[]> {
    try {
      const requests = await this.getRequests();
      const searchTerm = query.toLowerCase();
      
      return requests.filter(request =>
        request.url.toLowerCase().includes(searchTerm) ||
        request.method.toLowerCase().includes(searchTerm) ||
        (request.status?.toString() || '').includes(searchTerm) ||
        request.type.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching requests:', error);
      return [];
    }
  }

  // Get requests by domain
  static async getRequestsByDomain(domain: string): Promise<RequestData[]> {
    try {
      const requests = await this.getRequests();
      
      return requests.filter(request => {
        try {
          const url = new URL(request.url);
          return url.hostname === domain;
        } catch {
          return false;
        }
      });
    } catch (error) {
      console.error('Error getting requests by domain:', error);
      return [];
    }
  }

  // Get unique domains from stored requests
  static async getUniqueDomains(): Promise<string[]> {
    try {
      const requests = await this.getRequests();
      const domains = new Set<string>();
      
      requests.forEach(request => {
        try {
          const url = new URL(request.url);
          domains.add(url.hostname);
        } catch {
          // Ignore invalid URLs
        }
      });
      
      return Array.from(domains).sort();
    } catch (error) {
      console.error('Error getting unique domains:', error);
      return [];
    }
  }
}
